const OpenAI = require('openai');
const fs = require('fs-extra');
const path = require('path');
const ffmpeg = require('fluent-ffmpeg');
const Video = require('../models/Video');
const Transcription = require('../models/Transcription');

class TranscriptionService {
  constructor() {
    if (process.env.OPENAI_API_KEY) {
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY
      });
    } else {
      console.warn('OPENAI_API_KEY not set - transcription will use placeholder text');
      this.openai = null;
    }
  }

  // Transcribe audio file using OpenAI Whisper
  async transcribeAudio(audioFilePath, language = 'en') {
    try {
      console.log(`Starting transcription for: ${audioFilePath}`);

      // Check if file exists
      if (!await fs.pathExists(audioFilePath)) {
        throw new Error(`Audio file not found: ${audioFilePath}`);
      }

      // If OpenAI is not available, return placeholder transcription
      if (!this.openai) {
        console.log('OpenAI not configured - returning placeholder transcription');
        return {
          text: `[Placeholder transcription for ${path.basename(audioFilePath)}] - This is a placeholder transcription. To get actual transcriptions, please set your OPENAI_API_KEY environment variable.`,
          language: language,
          duration: 0
        };
      }

      // Check file size (OpenAI has a 25MB limit)
      const stats = await fs.stat(audioFilePath);
      const fileSizeInMB = stats.size / (1024 * 1024);

      console.log(`Audio file size: ${fileSizeInMB.toFixed(2)}MB`);

      if (fileSizeInMB > 25) {
        throw new Error(`Audio file too large: ${fileSizeInMB.toFixed(2)}MB. Maximum size is 25MB.`);
      }

      const startTime = Date.now();

      // Create a readable stream for the audio file
      const audioStream = fs.createReadStream(audioFilePath);

      // Call OpenAI Whisper API with optimized settings
      const transcription = await this.openai.audio.transcriptions.create({
        file: audioStream,
        model: 'whisper-1',  // Most reliable and fastest for general use
        language: language,
        response_format: 'json',  // Simplified format for faster processing
        // Removed timestamp_granularities and verbose_json for speed
        prompt: language === 'en' ? 'This is an educational video transcript.' : undefined  // Helps with accuracy
      });

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      console.log(`Transcription completed in ${processingTime}ms`);

      return {
        text: transcription.text,
        language: transcription.language,
        duration: transcription.duration || 0,
        words: transcription.words || [],
        processingTime: processingTime
      };

    } catch (error) {
      console.error('Transcription error:', error);
      throw new Error(`Transcription failed: ${error.message}`);
    }
  }

  // Split large audio files into smaller chunks for faster processing
  async splitAudioFile(audioFilePath, chunkDurationMinutes = 10) {
    const chunks = [];
    const tempDir = path.join(path.dirname(audioFilePath), 'temp_chunks');

    try {
      await fs.ensureDir(tempDir);

      return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(audioFilePath, (err, metadata) => {
          if (err) {
            reject(new Error(`Failed to get audio metadata: ${err.message}`));
            return;
          }

          const duration = metadata.format.duration;
          const chunkDuration = chunkDurationMinutes * 60; // Convert to seconds
          const numChunks = Math.ceil(duration / chunkDuration);

          console.log(`Splitting ${duration}s audio into ${numChunks} chunks of ${chunkDurationMinutes} minutes each`);

          let processedChunks = 0;

          for (let i = 0; i < numChunks; i++) {
            const startTime = i * chunkDuration;
            const chunkPath = path.join(tempDir, `chunk_${i}.mp3`);

            ffmpeg(audioFilePath)
              .seekInput(startTime)
              .duration(chunkDuration)
              .output(chunkPath)
              .on('end', () => {
                chunks.push({
                  path: chunkPath,
                  startTime: startTime,
                  index: i
                });
                processedChunks++;

                if (processedChunks === numChunks) {
                  resolve(chunks.sort((a, b) => a.index - b.index));
                }
              })
              .on('error', (error) => {
                reject(new Error(`Failed to create chunk ${i}: ${error.message}`));
              })
              .run();
          }
        });
      });
    } catch (error) {
      throw new Error(`Failed to split audio file: ${error.message}`);
    }
  }

  // Transcribe large files by splitting them into chunks
  async transcribeLargeAudio(audioFilePath, language = 'en') {
    try {
      const stats = await fs.stat(audioFilePath);
      const fileSizeInMB = stats.size / (1024 * 1024);

      // If file is smaller than 20MB, use regular transcription
      if (fileSizeInMB < 20) {
        return await this.transcribeAudio(audioFilePath, language);
      }

      console.log(`Large file detected (${fileSizeInMB.toFixed(2)}MB), splitting into chunks...`);

      const chunks = await this.splitAudioFile(audioFilePath, 8); // 8-minute chunks
      const transcriptionResults = [];

      for (const chunk of chunks) {
        console.log(`Transcribing chunk ${chunk.index + 1}/${chunks.length}...`);
        const result = await this.transcribeAudio(chunk.path, language);
        transcriptionResults.push({
          ...result,
          startTime: chunk.startTime,
          index: chunk.index
        });
      }

      // Cleanup temp files
      const tempDir = path.dirname(chunks[0].path);
      await fs.remove(tempDir);

      // Combine results
      const combinedText = transcriptionResults
        .sort((a, b) => a.index - b.index)
        .map(result => result.text)
        .join(' ');

      const totalProcessingTime = transcriptionResults.reduce((sum, result) => sum + result.processingTime, 0);

      return {
        text: combinedText,
        language: transcriptionResults[0]?.language || language,
        duration: transcriptionResults.reduce((sum, result) => sum + (result.duration || 0), 0),
        words: [], // Words not available for chunked transcription
        processingTime: totalProcessingTime,
        chunked: true,
        chunkCount: chunks.length
      };

    } catch (error) {
      throw new Error(`Failed to transcribe large audio: ${error.message}`);
    }
  }

  // Process video transcription and save to database
  async processVideoTranscription(videoId, audioPath, chapterId = null, countNumber = null) {
    try {
      console.log(`Processing transcription for video ID: ${videoId}`);

      // Get or create video record
      let video = await Video.findByYouTubeId(videoId);

      if (!video) {
        throw new Error(`Video not found: ${videoId}`);
      }

      // Update video status
      await video.update({
        status: 'transcribing',
        audio_path: audioPath,
        chapter_id: chapterId
      });

      // Check if transcription already exists
      const existingTranscription = await video.getTranscription();
      if (existingTranscription) {
        console.log(`Transcription already exists for video: ${videoId}`);
        await video.update({ status: 'completed' });
        return existingTranscription;
      }

      // Transcribe the audio (using optimized method for large files)
      const transcriptionResult = await this.transcribeLargeAudio(audioPath);

      // Generate transcription title
      const transcriptionTitle = this.generateTranscriptionTitle(video.title, countNumber);

      // Save transcription to database
      const transcription = await Transcription.create({
        video_id: video.id,
        title: transcriptionTitle,
        content: transcriptionResult.text,
        language: transcriptionResult.language,
        confidence_score: null, // Whisper doesn't provide confidence scores
        processing_time: transcriptionResult.processingTime
      });

      // Update video status to completed
      await video.update({ status: 'completed' });

      console.log(`Transcription saved successfully for video: ${videoId}`);

      return transcription;

    } catch (error) {
      // Update video status to failed
      try {
        const video = await Video.findByYouTubeId(videoId);
        if (video) {
          await video.update({ status: 'failed' });
        }
      } catch (updateError) {
        console.error('Failed to update video status:', updateError);
      }

      throw new Error(`Failed to process video transcription: ${error.message}`);
    }
  }

  // Generate transcription title based on video title
  generateTranscriptionTitle(videoTitle, countNumber = null) {
    if (countNumber) {
      return `${countNumber} - ${videoTitle}`;
    }
    // Fallback to old method for single videos
    const transcriptionNumber = this.getNextTranscriptionNumber();
    return `Transcription ${transcriptionNumber} - ${videoTitle}`;
  }

  // Get next transcription number (simplified - could be improved)
  getNextTranscriptionNumber() {
    // This is a simplified implementation
    // In a real application, you might want to track this more systematically
    return Date.now().toString().slice(-6); // Use timestamp as unique number
  }

  // Batch process multiple videos
  async batchProcessVideos(videoIds, chapterId = null) {
    const results = [];
    const errors = [];

    for (const videoId of videoIds) {
      try {
        console.log(`Processing video ${videoId} (${videoIds.indexOf(videoId) + 1}/${videoIds.length})`);
        
        const video = await Video.findByYouTubeId(videoId);
        if (!video || !video.audio_path) {
          throw new Error(`Video or audio path not found for: ${videoId}`);
        }

        const transcription = await this.processVideoTranscription(
          videoId, 
          video.audio_path, 
          chapterId
        );
        
        results.push({
          videoId,
          transcriptionId: transcription.id,
          success: true
        });

      } catch (error) {
        console.error(`Failed to process video ${videoId}:`, error);
        errors.push({
          videoId,
          error: error.message,
          success: false
        });
      }
    }

    return {
      successful: results,
      failed: errors,
      total: videoIds.length,
      successCount: results.length,
      failureCount: errors.length
    };
  }

  // Get transcription statistics
  async getTranscriptionStats() {
    try {
      const allTranscriptions = await Transcription.findAll();
      
      const stats = {
        total: allTranscriptions.length,
        totalProcessingTime: 0,
        averageProcessingTime: 0,
        languages: {},
        byChapter: {}
      };

      for (const transcription of allTranscriptions) {
        // Processing time stats
        if (transcription.processing_time) {
          stats.totalProcessingTime += transcription.processing_time;
        }

        // Language stats
        const lang = transcription.language || 'unknown';
        stats.languages[lang] = (stats.languages[lang] || 0) + 1;

        // Chapter stats (would need to join with videos table)
        const video = await transcription.getVideo();
        if (video && video.chapter_id) {
          const chapter = await video.getChapter();
          if (chapter) {
            stats.byChapter[chapter.name] = (stats.byChapter[chapter.name] || 0) + 1;
          }
        }
      }

      if (stats.total > 0) {
        stats.averageProcessingTime = stats.totalProcessingTime / stats.total;
      }

      return stats;
    } catch (error) {
      throw new Error(`Failed to get transcription stats: ${error.message}`);
    }
  }

  // Clean up audio files after transcription
  async cleanupAudioFile(audioPath) {
    try {
      if (await fs.pathExists(audioPath)) {
        await fs.remove(audioPath);
        console.log(`Cleaned up audio file: ${audioPath}`);
      }
    } catch (error) {
      console.error('Failed to cleanup audio file:', error);
    }
  }

  // Validate audio file for transcription
  async validateAudioFile(audioPath) {
    try {
      if (!await fs.pathExists(audioPath)) {
        return { valid: false, error: 'File does not exist' };
      }

      const stats = await fs.stat(audioPath);
      const fileSizeInMB = stats.size / (1024 * 1024);

      if (fileSizeInMB > 25) {
        return { 
          valid: false, 
          error: `File too large: ${fileSizeInMB.toFixed(2)}MB (max 25MB)` 
        };
      }

      if (stats.size === 0) {
        return { valid: false, error: 'File is empty' };
      }

      return { valid: true };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }
}

module.exports = TranscriptionService;
