const ytdl = require('@distube/ytdl-core');
const ytpl = require('@distube/ytpl');
const fs = require('fs-extra');
const path = require('path');
const ffmpeg = require('fluent-ffmpeg');
const os = require('os');
const pLimit = require('p-limit');
const progressService = require('./progressService');

class YouTubeService {
  constructor() {
    this.downloadPath = path.join(__dirname, '..', '..', 'downloaded_videos');
    this.audioPath = path.join(__dirname, '..', '..', 'extracted_audios');

    // Configure parallel processing limits
    this.maxConcurrentDownloads = 3; // Process 3 videos simultaneously
    this.downloadLimit = pLimit(this.maxConcurrentDownloads);

    // Configure FFmpeg path
    this.setupFFmpeg();

    // Ensure directories exist
    fs.ensureDirSync(this.downloadPath);
    fs.ensureDirSync(this.audioPath);
  }

  setupFFmpeg() {
    // Try to find FFmpeg in common locations
    const possiblePaths = [
      // Winget installation path
      path.join(process.env.LOCALAPPDATA, 'Microsoft', 'WinGet', 'Packages', 'Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe', 'ffmpeg-7.1.1-full_build', 'bin', 'ffmpeg.exe'),
      // System PATH (if available)
      'ffmpeg',
      // Other common locations
      'C:\\ffmpeg\\bin\\ffmpeg.exe',
      'C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe'
    ];

    for (const ffmpegPath of possiblePaths) {
      try {
        if (ffmpegPath === 'ffmpeg' || fs.existsSync(ffmpegPath)) {
          ffmpeg.setFfmpegPath(ffmpegPath);
          console.log(`FFmpeg configured at: ${ffmpegPath}`);
          return;
        }
      } catch (error) {
        // Continue to next path
      }
    }

    console.warn('FFmpeg not found in common locations. Audio extraction may fail.');
  }

  // Extract video ID from YouTube URL
  extractVideoId(url) {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }

  // Extract playlist ID from YouTube URL
  extractPlaylistId(url) {
    const regex = /[&?]list=([^&]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }

  // Check if URL is a playlist
  isPlaylistUrl(url) {
    return url.includes('list=');
  }

  // Get video information
  async getVideoInfo(videoId) {
    try {
      const info = await ytdl.getInfo(videoId);
      return {
        id: videoId,
        title: info.videoDetails.title,
        description: info.videoDetails.description,
        duration: parseInt(info.videoDetails.lengthSeconds),
        author: info.videoDetails.author.name,
        viewCount: parseInt(info.videoDetails.viewCount),
        uploadDate: info.videoDetails.uploadDate
      };
    } catch (error) {
      throw new Error(`Failed to get video info: ${error.message}`);
    }
  }

  // Get playlist information
  async getPlaylistInfo(playlistId) {
    try {
      console.log(`Fetching playlist info for: ${playlistId}`);
      const playlist = await ytpl(playlistId);

      return {
        id: playlist.id,
        title: playlist.title,
        description: playlist.description,
        videoCount: playlist.estimatedItemCount,
        videos: playlist.items.map(item => ({
          id: item.id,
          title: item.title,
          url: item.shortUrl,
          duration: item.duration,
          thumbnail: item.thumbnail
        }))
      };
    } catch (error) {
      throw new Error(`Failed to get playlist info: ${error.message}`);
    }
  }

  // Download video with progress tracking
  async downloadVideo(videoId, quality = 'highest', jobId = null, countNumber = null) {
    try {
      const info = await this.getVideoInfo(videoId);
      const sanitizedTitle = this.sanitizeFilename(info.title);
      const filename = countNumber
        ? `${countNumber} - ${sanitizedTitle}.mp4`
        : `${videoId}_${sanitizedTitle}.mp4`;
      const filePath = path.join(this.downloadPath, filename);

      // Update progress if job tracking is enabled
      if (jobId) {
        progressService.updateVideoProgress(jobId, videoId, {
          title: info.title,
          status: 'downloading',
          progress: 0,
          phase: 'download'
        });
      }

      // Check if file already exists
      if (await fs.pathExists(filePath)) {
        console.log(`Video already exists: ${filename}`);

        if (jobId) {
          progressService.updateVideoProgress(jobId, videoId, {
            status: 'download_complete',
            progress: 50,
            phase: 'download'
          });
        }

        return {
          success: true,
          filePath,
          info
        };
      }

      return await this.downloadWithYtdlCore(videoId, filePath, info, quality, jobId);
    } catch (error) {
      if (jobId) {
        progressService.updateVideoProgress(jobId, videoId, {
          status: 'failed',
          error: error.message,
          phase: 'download'
        });
      }
      throw new Error(`Failed to download video: ${error.message}`);
    }
  }

  // Download using ytdl-core with progress tracking
  async downloadWithYtdlCore(videoId, filePath, info, quality = 'highest', jobId = null) {
    return new Promise((resolve, reject) => {
      const stream = ytdl(videoId, {
        quality: quality === 'highest' ? 'highestvideo' : quality,
        filter: 'videoandaudio'
      });

      const writeStream = fs.createWriteStream(filePath);

      stream.pipe(writeStream);

      stream.on('error', (error) => {
        console.error('Download stream error:', error);
        reject(new Error(`Download failed: ${error.message}`));
      });

      writeStream.on('error', (error) => {
        console.error('Write stream error:', error);
        reject(new Error(`File write failed: ${error.message}`));
      });

      writeStream.on('finish', () => {
        console.log(`Video downloaded successfully with ytdl-core: ${path.basename(filePath)}`);

        if (jobId) {
          progressService.updateVideoProgress(jobId, videoId, {
            status: 'download_complete',
            progress: 50,
            phase: 'download'
          });
        }

        resolve({
          success: true,
          filePath,
          info
        });
      });

      // Track download progress with real-time updates
      stream.on('progress', (_, downloaded, total) => {
        const percent = Math.round((downloaded / total) * 50); // Download is 50% of total progress
        console.log(`Download progress: ${percent}% (${downloaded}/${total} bytes)`);

        if (jobId) {
          progressService.updateVideoProgress(jobId, videoId, {
            progress: percent,
            downloaded,
            total,
            phase: 'download'
          });
        }
      });
    });
  }



  // Extract audio from video with progress tracking
  async extractAudio(videoPath, videoId, title, jobId = null, countNumber = null) {
    try {
      const sanitizedTitle = this.sanitizeFilename(title);
      const audioFilename = countNumber
        ? `${countNumber} - ${sanitizedTitle}.mp3`
        : `${videoId}_${sanitizedTitle}.mp3`;
      const audioFilePath = path.join(this.audioPath, audioFilename);

      if (jobId) {
        progressService.updateVideoProgress(jobId, videoId, {
          status: 'extracting_audio',
          progress: 50,
          phase: 'audio_extraction'
        });
      }

      // Check if audio file already exists
      if (await fs.pathExists(audioFilePath)) {
        console.log(`Audio already exists: ${audioFilename}`);

        if (jobId) {
          progressService.updateVideoProgress(jobId, videoId, {
            status: 'completed',
            progress: 100,
            phase: 'completed'
          });
        }

        return {
          success: true,
          audioPath: audioFilePath
        };
      }

      return new Promise((resolve, reject) => {
        ffmpeg(videoPath)
          .toFormat('mp3')
          .audioBitrate(64)  // Reduced from 128 - sufficient for speech
          .audioChannels(1)  // Mono instead of stereo - faster processing
          .audioFrequency(16000)  // Reduced from 44100 - optimal for speech recognition
          .on('start', (commandLine) => {
            console.log('FFmpeg started with command:', commandLine);
          })
          .on('progress', (progress) => {
            const totalProgress = 50 + Math.round((progress.percent || 0) / 2); // 50-100% range
            console.log(`Audio extraction progress: ${progress.percent}%`);

            if (jobId) {
              progressService.updateVideoProgress(jobId, videoId, {
                progress: totalProgress,
                phase: 'audio_extraction'
              });
            }
          })
          .on('end', () => {
            console.log(`Audio extracted successfully: ${audioFilename}`);

            if (jobId) {
              progressService.updateVideoProgress(jobId, videoId, {
                status: 'completed',
                progress: 100,
                phase: 'completed'
              });
            }

            resolve({
              success: true,
              audioPath: audioFilePath
            });
          })
          .on('error', (error) => {
            console.error('FFmpeg error:', error);

            if (jobId) {
              progressService.updateVideoProgress(jobId, videoId, {
                status: 'failed',
                error: error.message,
                phase: 'audio_extraction'
              });
            }

            reject(new Error(`Audio extraction failed: ${error.message}`));
          })
          .save(audioFilePath);
      });
    } catch (error) {
      if (jobId) {
        progressService.updateVideoProgress(jobId, videoId, {
          status: 'failed',
          error: error.message,
          phase: 'audio_extraction'
        });
      }
      throw new Error(`Failed to extract audio: ${error.message}`);
    }
  }

  // Process single video (download + extract audio) with optional job tracking
  async processVideo(url, chapterId = null, jobId = null, countNumber = null) {
    try {
      const videoId = this.extractVideoId(url);
      if (!videoId) {
        throw new Error('Invalid YouTube URL');
      }

      console.log(`Processing video: ${videoId}`);

      // Get video information
      const info = await this.getVideoInfo(videoId);

      // Download video
      const downloadResult = await this.downloadVideo(videoId, 'highest', jobId, countNumber);

      // Extract audio
      const audioResult = await this.extractAudio(
        downloadResult.filePath,
        videoId,
        info.title,
        jobId,
        countNumber
      );

      return {
        success: true,
        videoId,
        info,
        videoPath: downloadResult.filePath,
        audioPath: audioResult.audioPath,
        chapterId
      };
    } catch (error) {
      throw new Error(`Failed to process video: ${error.message}`);
    }
  }

  // Process playlist with parallel downloads and progress tracking
  async processPlaylist(url, chapterId = null, jobId = null) {
    try {
      const playlistId = this.extractPlaylistId(url);
      if (!playlistId) {
        throw new Error('Invalid playlist URL');
      }

      console.log(`Processing playlist: ${playlistId}`);

      // Get playlist information
      const playlistInfo = await this.getPlaylistInfo(playlistId);
      console.log(`Found ${playlistInfo.videos.length} videos in playlist: ${playlistInfo.title}`);

      // Create or update job progress
      if (jobId) {
        progressService.updateJob(jobId, {
          status: 'processing',
          totalSteps: playlistInfo.videos.length,
          data: {
            playlistTitle: playlistInfo.title,
            playlistId: playlistInfo.id
          }
        });
      }

      const results = [];
      const errors = [];

      // Process videos in parallel with concurrency limit
      const processVideoWithLimit = async (video, index) => {
        // Ensure video object has the required properties
        if (!video || !video.id) {
          console.error(`Invalid video object at index ${index}:`, video);
          return null;
        }

        const videoUrl = `https://www.youtube.com/watch?v=${video.id}`;
        console.log(`Processing video ${index + 1}/${playlistInfo.videos.length}: ${video.title || video.id}`);

        try {
          const countNumber = index + 1;
          const result = await this.processVideo(videoUrl, chapterId, jobId, countNumber);
          results.push({
            ...result,
            playlistIndex: countNumber,
            playlistTitle: playlistInfo.title
          });
          return result;
        } catch (error) {
          console.error(`Failed to process video ${video.title || video.id}:`, error.message);
          const errorInfo = {
            video: video.title || video.id,
            error: error.message,
            index: index + 1
          };
          errors.push(errorInfo);

          if (jobId) {
            progressService.updateVideoProgress(jobId, video.id, {
              title: video.title || video.id,
              status: 'failed',
              error: error.message,
              progress: 0
            });
          }

          return null;
        }
      };

      // Process all videos with controlled concurrency
      const promises = playlistInfo.videos.map((video, index) =>
        this.downloadLimit(processVideoWithLimit, video, index)
      );

      await Promise.allSettled(promises);

      const finalResult = {
        playlist: {
          id: playlistInfo.id,
          title: playlistInfo.title,
          totalVideos: playlistInfo.videos.length
        },
        results,
        errors,
        summary: {
          successful: results.length,
          failed: errors.length,
          total: playlistInfo.videos.length
        }
      };

      // Complete the job
      if (jobId) {
        const status = errors.length > 0 ? 'completed_with_errors' : 'completed';
        progressService.completeJob(jobId, status, finalResult);
      }

      return finalResult;

    } catch (error) {
      if (jobId) {
        progressService.completeJob(jobId, 'failed', { error: error.message });
      }
      throw new Error(`Failed to process playlist: ${error.message}`);
    }
  }

  // Sanitize filename for file system
  sanitizeFilename(filename) {
    return filename
      .replace(/[<>:"/\\|?*]/g, '') // Remove invalid characters
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .substring(0, 100); // Limit length
  }

  // Clean up files
  async cleanupFiles(videoPath, audioPath) {
    try {
      if (videoPath && await fs.pathExists(videoPath)) {
        await fs.remove(videoPath);
        console.log(`Cleaned up video file: ${videoPath}`);
      }
      if (audioPath && await fs.pathExists(audioPath)) {
        await fs.remove(audioPath);
        console.log(`Cleaned up audio file: ${audioPath}`);
      }
    } catch (error) {
      console.error('Cleanup error:', error);
    }
  }

  // Get file size
  async getFileSize(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }
}

module.exports = new YouTubeService();
